import json
import os
from datetime import datetime
from typing import List, Dict, Optional
import threading


class DataManager:
    """Manages torrent and ratio data for the web interface"""
    
    def __init__(self):
        self.torrent_log_file = 'torrent_log.json'
        self.ratio_file = 'ratio.txt'
        self.downloaded_file = 'downloaded.json'
        self.lock = threading.Lock()
        
        # Initialize data structures
        self.torrent_log = self._load_torrent_log()
        self.ratio_history = self._load_ratio_history()
        self.downloaded_torrents = self._load_downloaded_torrents()
    
    def _load_torrent_log(self) -> List[Dict]:
        """Load torrent log from JSON file"""
        if os.path.exists(self.torrent_log_file):
            try:
                with open(self.torrent_log_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    # Filter out test data
                    return [entry for entry in data if not entry.get('file_id', '').startswith('test')]
            except (json.JSONDecodeError, FileNotFoundError):
                return []
        return []
    
    def _save_torrent_log(self):
        """Save torrent log to JSON file"""
        with open(self.torrent_log_file, 'w', encoding='utf-8') as f:
            json.dump(self.torrent_log, f, indent=2, ensure_ascii=False)
    
    def _load_ratio_history(self) -> List[Dict]:
        """Load ratio history from ratio.txt file (last 500 entries for performance)"""
        ratio_data = []
        if os.path.exists(self.ratio_file):
            try:
                with open(self.ratio_file, 'r') as f:
                    lines = f.readlines()
                    # Only process the last 500 lines for performance
                    lines = lines[-500:] if len(lines) > 500 else lines

                    for line in lines:
                        line = line.strip()
                        if line and ' - ' in line:
                            try:
                                ratio_str, timestamp_str = line.split(' - ', 1)
                                ratio = float(ratio_str)
                                # Parse timestamp: DD.MM.YYYY-HH:MM:SS
                                timestamp = datetime.strptime(timestamp_str, '%d.%m.%Y-%H:%M:%S')
                                ratio_data.append({
                                    'ratio': ratio,
                                    'timestamp': timestamp.isoformat(),
                                    'timestamp_display': timestamp_str
                                })
                            except (ValueError, TypeError) as e:
                                # Skip invalid lines
                                continue
            except FileNotFoundError:
                pass
        return ratio_data
    
    def _load_downloaded_torrents(self) -> set:
        """Load downloaded torrent IDs from JSON file"""
        if os.path.exists(self.downloaded_file):
            try:
                with open(self.downloaded_file, 'r') as f:
                    return set(json.load(f))
            except (json.JSONDecodeError, FileNotFoundError):
                return set()
        return set()
    
    def add_torrent_log_entry(self, name: str, file_id: str, size_bytes: int, category_id: str, seeds: int):
        """Add a new torrent log entry"""
        with self.lock:
            # Format size for display
            size_gb = size_bytes / (1024 ** 3)
            if size_gb >= 1:
                size_display = f"{size_gb:.2f} GB"
            else:
                size_mb = size_bytes / (1024 ** 2)
                size_display = f"{size_mb:.2f} MB"
            
            entry = {
                'name': name,
                'file_id': file_id,
                'size_bytes': size_bytes,
                'size_display': size_display,
                'category_id': category_id,
                'seeds': seeds,
                'date_added': datetime.now().isoformat(),
                'date_added_display': datetime.now().strftime('%d.%m.%Y %H:%M:%S')
            }
            
            # Add to beginning of list (most recent first)
            self.torrent_log.insert(0, entry)
            
            # Keep only last 1000 entries to prevent file from growing too large
            if len(self.torrent_log) > 1000:
                self.torrent_log = self.torrent_log[:1000]
            
            self._save_torrent_log()
    
    def update_ratio(self, ratio: float, timestamp_str: str):
        """Update ratio history"""
        with self.lock:
            # Parse timestamp
            try:
                timestamp = datetime.strptime(timestamp_str, '%d.%m.%Y-%H:%M:%S')
                entry = {
                    'ratio': ratio,
                    'timestamp': timestamp.isoformat(),
                    'timestamp_display': timestamp_str
                }
                
                # Add to end of list (chronological order)
                self.ratio_history.append(entry)
                
                # Keep only last 500 entries for performance
                if len(self.ratio_history) > 500:
                    self.ratio_history = self.ratio_history[-500:]
                    
            except ValueError:
                pass
    
    def get_torrent_log(self, limit: Optional[int] = None) -> List[Dict]:
        """Get torrent log entries"""
        with self.lock:
            if limit:
                return self.torrent_log[:limit]
            return self.torrent_log.copy()
    
    def get_ratio_history(self, limit: Optional[int] = None) -> List[Dict]:
        """Get ratio history"""
        with self.lock:
            if limit:
                return self.ratio_history[-limit:]
            return self.ratio_history.copy()
    
    def get_latest_ratio(self) -> Optional[float]:
        """Get the latest ratio value"""
        try:
            with self.lock:
                if self.ratio_history:
                    return self.ratio_history[-1]['ratio']
                return None
        except Exception:
            return None
    
    def clean_test_data(self):
        """Remove test data from torrent log"""
        with self.lock:
            original_count = len(self.torrent_log)
            self.torrent_log = [entry for entry in self.torrent_log if not entry.get('file_id', '').startswith('test')]
            if len(self.torrent_log) != original_count:
                self._save_torrent_log()
                print(f"Cleaned {original_count - len(self.torrent_log)} test entries from torrent log")

    def get_stats(self) -> Dict:
        """Get summary statistics"""
        try:
            with self.lock:
                total_torrents = len(self.torrent_log)
                total_size_bytes = sum(entry['size_bytes'] for entry in self.torrent_log)
                total_size_gb = total_size_bytes / (1024 ** 3)

                latest_ratio = None
                if self.ratio_history:
                    latest_ratio = self.ratio_history[-1]['ratio']

                # Calculate ratio change (last 24 hours if possible)
                ratio_change = 0
                if len(self.ratio_history) > 1:
                    current_time = datetime.now()
                    day_ago = current_time.timestamp() - (24 * 60 * 60)

                    # Find ratio from ~24 hours ago
                    day_ago_ratio = None
                    for entry in reversed(self.ratio_history):
                        try:
                            entry_time = datetime.fromisoformat(entry['timestamp']).timestamp()
                            if entry_time <= day_ago:
                                day_ago_ratio = entry['ratio']
                                break
                        except Exception:
                            continue

                    if day_ago_ratio and latest_ratio:
                        ratio_change = latest_ratio - day_ago_ratio

                return {
                    'total_torrents': total_torrents,
                    'total_size_gb': round(total_size_gb, 2),
                    'latest_ratio': latest_ratio,
                    'ratio_change_24h': round(ratio_change, 3)
                }
        except Exception as e:
            # Return default stats if there's an error
            return {
                'total_torrents': 0,
                'total_size_gb': 0,
                'latest_ratio': None,
                'ratio_change_24h': 0
            }


# Global instance - lazy initialization
data_manager = None

def get_data_manager():
    """Get or create the global data manager instance"""
    global data_manager
    if data_manager is None:
        data_manager = DataManager()
    return data_manager
