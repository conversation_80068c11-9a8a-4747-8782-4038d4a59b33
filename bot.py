import os
import time
import yaml
import signal
import sys
from dotenv import load_dotenv
from selenium import webdriver
from selenium.webdriver.common.by import By
import undetected_chromedriver as uc
import json
import argparse
import logging
from datetime import datetime
from selenium.webdriver.common.keys import Keys
from web_server import initialize_web_server, get_web_server

load_dotenv()

parser = argparse.ArgumentParser(description='YggTorrent Bot')
parser.add_argument('--debug', action='store_true', help='Enable debug mode (visible browser and logging)')
parser.add_argument('--no-web', action='store_true', help='Disable web interface')
parser.add_argument('--web-port', type=int, help='Web interface port (overrides config)')
args = parser.parse_args()

if args.debug:
    print("=" * 50)
    print("YGG TORRENT BOT STARTING...")
    print(f"Debug mode: {'ENABLED' if args.debug else 'DISABLED'}")
    print("=" * 50)

if args.debug:
    logging.basicConfig(filename='debug.log', level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
    logging.debug('Debug mode enabled')
    print("Debug logging to debug.log enabled")
else:
    logging.basicConfig(level=logging.WARNING)
    # No silent mode message in normal mode to minimize output

def debug_print(msg):
    if args.debug:
        print(msg)

username = os.getenv('USERNAME')
password = os.getenv('PASSWORD')

if not username or not password:
    print("✗ ERROR: Missing credentials!")
    print("Please set USERNAME and PASSWORD environment variables")
    sys.exit(1)
else:
    debug_print("✓ Credentials loaded from environment")
sys.stdout.flush()
debug_print("Setting up browser options...")
debug_print("Creating ChromeOptions object...")
options = uc.ChromeOptions()
debug_print("✓ ChromeOptions object created")
debug_print("Adding --no-sandbox argument...")
options.add_argument('--no-sandbox')
debug_print("✓ Added --no-sandbox")
debug_print("Adding --disable-dev-shm-usage...")
options.add_argument('--disable-dev-shm-usage')
debug_print("✓ Added --disable-dev-shm-usage")
debug_print("Adding --disable-gpu...")
options.add_argument('--disable-gpu')
debug_print("✓ Added --disable-gpu")
debug_print("Adding user-data-dir...")
debug_print("Using temporary profile directory to avoid volume issues")
profile_dir = '/tmp/ygg_chrome_profile'
options.add_argument(f'--user-data-dir={profile_dir}')  # For persistent session
debug_print("✓ Added user-data-dir")
debug_print("Adding experimental prefs...")
options.add_experimental_option("prefs", {
    "download.default_directory": "/Volumes/1To/ygg/torrents",
    "download.prompt_for_download": False,
    "download.directory_upgrade": True,
    "safebrowsing.enabled": False
})
debug_print("✓ Added experimental prefs")
debug_print("Checking debug mode for headless...")
if not args.debug:
    options.add_argument('--headless=new')
    debug_print("✓ Added --headless=new (headless mode)")
else:
    logging.debug('Running with visible browser')
    debug_print("✓ Visible browser mode enabled")

# Load config
debug_print("Loading configuration from config.yaml...")
try:
    with open('config.yaml', 'r') as f:
        config = yaml.safe_load(f)
    debug_print("✓ Configuration loaded successfully")
except Exception as e:
    print(f"✗ Failed to load configuration: {e}")
    # No driver to quit at this point since it hasn't been initialized yet
    sys.exit(1)

min_size_gb = config['minimum_size_gb']
max_size_gb = config['maximum_size_gb']
max_seed = config['maximum_seed']
max_time = config['max_time']
ratio = 0.0

# Initialize web server if enabled
web_server = None
if not args.no_web and config.get('web_server', {}).get('enabled', True):
    web_config = config.get('web_server', {})
    web_host = web_config.get('host', '127.0.0.1')
    web_port = args.web_port or web_config.get('port', 8080)

    try:
        # Clean test data before starting web server
        from data_manager import get_data_manager
        get_data_manager().clean_test_data()

        web_server = initialize_web_server(web_host, web_port, args.debug)
        web_server.start()
        debug_print(f"✓ Web interface started at http://{web_host}:{web_port}")
    except Exception as e:
        debug_print(f"⚠ Failed to start web interface: {e}")
        web_server = None
debug_print("Initializing Chrome driver...")
try:
    driver = uc.Chrome(options=options, version_main=138)
    debug_print("✓ Chrome driver initialized successfully")
except Exception as e:
    print(f"✗ Failed to initialize Chrome driver: {e}")
    sys.exit(1)
logging.debug('Chrome driver initialized')

# Navigate to the login page
debug_print("Starting browser and navigating to login page...")
try:
    driver.set_page_load_timeout(30)
    driver.get('https://www.yggtorrent.top/auth/login')
    debug_print("✓ Login page loaded successfully")
except Exception as e:
    print(f"✗ Failed to load login page: {e}")
    driver.quit()
    sys.exit(1)

from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
logging.debug('Imported WebDriverWait and EC')

# Set up wait with reasonable timeout
wait = WebDriverWait(driver, 15)
debug_print("Checking page elements...")

# Handle login process
try:
    wait = WebDriverWait(driver, 10)
    debug_print("Checking login status...")
    
    # Check for Cloudflare challenge
    debug_print("Checking for Cloudflare challenge...")
    if "cloudflare" in driver.page_source.lower() or "checking your browser" in driver.page_source.lower():
        debug_print("⚠ Cloudflare challenge detected!")
        debug_print("Waiting for Cloudflare to complete...")
        try:
            wait.until_not(lambda d: "cloudflare" in d.page_source.lower() or "checking your browser" in d.page_source.lower())
            debug_print("✓ Cloudflare challenge completed")
        except:
            print("✗ Timeout waiting for Cloudflare to complete")
            driver.quit()
            sys.exit(1)
    
    # Check if already logged in
    debug_print("Looking for logout button...")
    try:
        wait_short = WebDriverWait(driver, 5)
        wait_short.until(EC.presence_of_element_located((By.XPATH, "//a[contains(text(), 'Déconnexion')]")))
        debug_print("✓ Already logged in, skipping login")
        login_needed = False
    except:
        debug_print("✗ Not logged in, proceeding with login")
        login_needed = True
    
    # Perform login if needed
    if login_needed:
        try:
            debug_print("Looking for username field...")
            username_input = wait.until(EC.presence_of_element_located((By.NAME, 'id')))
            debug_print("✓ Username field found")
            
            debug_print("Looking for password field...")
            password_input = wait.until(EC.presence_of_element_located((By.NAME, 'pass')))
            debug_print("✓ Password field found")
            
            # Use JavaScript to set values directly and disable autofill
            debug_print("Setting credentials using JavaScript to avoid autofill issues...")
            try:
                driver.execute_script("""
                    var usernameField = document.querySelector('input[name="id"]');
                    var passwordField = document.querySelector('input[name="pass"]');
                    
                    if (!usernameField || !passwordField) {
                        throw new Error('Login fields not found');
                    }
                    
                    // Disable autofill
                    usernameField.setAttribute('autocomplete', 'off');
                    passwordField.setAttribute('autocomplete', 'off');
                    
                    // Clear and set values
                    usernameField.value = '';
                    passwordField.value = '';
                    usernameField.value = arguments[0];
                    passwordField.value = arguments[1];
                    
                    // Trigger input events to ensure the form recognizes the values
                    usernameField.dispatchEvent(new Event('input', { bubbles: true }));
                    passwordField.dispatchEvent(new Event('input', { bubbles: true }));
                """, username, password)
                debug_print("✓ Credentials set using JavaScript")
            except Exception as js_error:
                debug_print(f"JavaScript method failed: {js_error}")
                debug_print("Falling back to Selenium method...")
                # Fallback to original method
                username_input.clear()
                username_input.send_keys(username)
                password_input.clear()
                password_input.send_keys(password)
                debug_print("✓ Credentials set using Selenium fallback")
            
            debug_print("Looking for login form...")
            form = wait.until(EC.presence_of_element_located((By.XPATH, '//form[@action="/auth/process_login"]')))
            debug_print("✓ Login form found")
            form.submit()
            
            debug_print("✓ Login form submitted successfully")
            debug_print("Waiting for login to complete...")
            time.sleep(5)  # Extended wait for login to process
        except Exception as login_error:
            print(f"✗ Login failed: {login_error}")
            print("This might be due to Cloudflare protection or incorrect credentials")
            print("Current page title:", driver.title)
            print("Try running with --debug flag for more details")
            raise login_error
    
    # Load downloaded file_ids
    downloaded_file = 'downloaded.json'
    if os.path.exists(downloaded_file):
        with open(downloaded_file, 'r') as f:
            downloaded = set(json.load(f))
        debug_print(f"✓ Loaded {len(downloaded)} previously downloaded torrents")
    else:
        downloaded = set()
        debug_print("✓ No previous downloads found")
    logging.debug('Loaded downloaded.json')
    
    # Disable cache to ensure fresh page load
    driver.execute_cdp_cmd('Network.setCacheDisabled', {'cacheDisabled': True})

    # Set up signal handler for graceful shutdown
    def signal_handler(sig, frame):
        print('\nGracefully shutting down...')
        logging.info('Received shutdown signal')
        if web_server:
            web_server.stop()
        driver.quit()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    first_run = True
    debug_print("✓ Starting main loop...")

    while True:
            
            try:
                # Navigate/refresh the target page
                debug_print("Refreshing torrent page...")
                driver.get('https://www.yggtorrent.top/top/day')
                logging.debug('Navigated to top/day')
                
                debug_print(f"Page loaded: {driver.current_url}")
                if first_run:
                    debug_print(f"Page title: {driver.title}")
                    
                # Wait for the table to load with shorter timeout
                try:
                    wait_short = WebDriverWait(driver, 15)
                    wait_short.until(EC.presence_of_element_located((By.CSS_SELECTOR, 'tr.odd, tr.even')))
                    debug_print("✓ Torrent table loaded")
                except:
                    debug_print("⚠ Timeout waiting for torrent table, retrying...")
                    time.sleep(5)
                    continue

                # Extract ratio
                try:
                    debug_print("Looking for ratio element...")
                    ratio_element = driver.find_element(By.XPATH, '//li/a[contains(@href, "/user/donate")]/strong')
                    ratio_text = ratio_element.text.strip().replace("Ratio : ", "")
                    ratio = float(ratio_text)
                    debug_print(f"✓ Current ratio: {ratio}")
                    logging.debug(f'Extracted ratio: {ratio}')

                    # Read last ratio from ratio.txt
                    last_ratio = None
                    if os.path.exists('ratio.txt'):
                        with open('ratio.txt', 'r') as f:
                            lines = f.readlines()
                            if lines:
                                last_line = lines[-1].strip()
                                last_ratio_str = last_line.split(' - ')[0]
                                last_ratio = float(last_ratio_str)
                    logging.debug(f'Last ratio: {last_ratio}')

                    if first_run:
                        print(f"Current ratio: {ratio}")
                        logging.debug(f'Current ratio: {ratio}')
                        debug_print("-----------")

                    if last_ratio is None or ratio != last_ratio:
                        from datetime import datetime
                        current_time = datetime.now().strftime('%d.%m.%Y-%H:%M:%S')
                        new_line = f"{ratio} - {current_time}\n"
                        with open('ratio.txt', 'a') as f:
                            f.write(new_line)
                        if not first_run:
                            if not args.debug:  # Only show ratio in non-debug mode when no torrents
                                print(f"{ratio} - {current_time}")
                        logging.debug(f'Updated ratio: {ratio}')

                        # Broadcast ratio update to web interface
                        if web_server:
                            web_server.broadcast_ratio_update(ratio, current_time)
                    
                    if first_run:
                        first_run = False
                except Exception as e:
                    debug_print(f"Error extracting ratio: {e}")
                    logging.error(f'Ratio extraction error: {e}')

                # Find all rows
                debug_print("Looking for torrent rows...")
                rows = driver.find_elements(By.CSS_SELECTOR, 'tr.odd, tr.even')
                debug_print(f"✓ Found {len(rows)} torrent rows")
                logging.debug(f'Found {len(rows)} rows')

                # Prepare data list
                data = []
                allowed_categories = {'2161', '2172', '2191', '2189', '2402'}
                debug_print(f"Processing {len(rows)} torrent rows...")
                processed_count = 0
                filtered_count = 0
                
                for i, row in enumerate(rows):
                    tds = row.find_elements(By.TAG_NAME, 'td')
                    if len(tds) < 6:
                        continue
                    
                    # Category ID
                    span = tds[0].find_element(By.TAG_NAME, 'span')
                    category_class = span.get_attribute('class')
                    category_id = category_class.split('tag_subcat_')[1] if category_class and 'tag_subcat_' in category_class else ''
                    
                    if category_id not in allowed_categories:
                        continue
                    
                    # Name
                    name = tds[1].find_element(By.TAG_NAME, 'a').text
                    
                    # File ID
                    nfo_a = tds[2].find_element(By.TAG_NAME, 'a')
                    file_id = nfo_a.get_attribute('target')
                    
                    # Timestamp
                    timestamp_div = tds[4].find_element(By.CLASS_NAME, 'hidden')
                    timestamp = timestamp_div.get_attribute('textContent')
                    
                    # Size
                    size_div = tds[5].find_element(By.CLASS_NAME, 'hidden')
                    size = size_div.get_attribute('textContent')

                    # Seeds (assuming tds[6] is seeds)
                    seeds = tds[6].text.strip()

                    if size is None or not size.isdigit() or seeds is None or not seeds.isdigit() or timestamp is None or not timestamp.isdigit():
                        continue

                    try:
                        timestamp_int = int(timestamp)
                        minutes_ago = int((time.time() - timestamp_int) / 60)
                        if minutes_ago >= max_time:
                            continue
                        size_bytes = int(size)
                        size_gb_calc = size_bytes / (1024 ** 3)
                        seeds_int = int(seeds)
                        if not (min_size_gb <= size_gb_calc <= max_size_gb) or seeds_int > max_seed:
                            continue
                    except ValueError:
                        continue

                    data.append((category_id, name, file_id, timestamp, size, seeds))
                    logging.debug(f'Appended data for {file_id}')
                    processed_count += 1
                
                debug_print(f"✓ Processed {processed_count} torrents, {len(data)} match criteria")

                # Print table
                if data:
                    if args.debug:
                        print('| Category ID | Name | File ID | Timestamp (min ago) | Size (bytes) (GB) | Seeds |')
                        print('|-------------|------|---------|---------------------|-------------------|-------|')
                        for item in data:
                            try:
                                timestamp_int = int(item[3])
                                minutes_ago = int((time.time() - timestamp_int) / 60)
                                timestamp_display = f"{item[3]} ({minutes_ago} min ago)"
                            except ValueError:
                                timestamp_display = item[3]
                            try:
                                size_bytes = int(item[4])
                                size_gb = size_bytes / (1024 ** 3)
                                size_display = f"{item[4]} ({size_gb:.2f} GB)"
                            except ValueError:
                                size_display = item[4]
                            print(f'| {item[0]} | {item[1]} | {item[2]} | {timestamp_display} | {size_display} | {item[5]} |')
                            logging.debug(f'Printed row for {item[2]}')
                elif 'ratio' in locals() and (args.debug or not web_server):
                    current_time = datetime.now().strftime('%d.%m.%Y-%H:%M:%S')
                    print(f"{ratio} - {current_time}")

                # Download torrents and track if any were downloaded
                torrents_downloaded = 0
                for item in data:
                    file_id = item[2]
                    if file_id not in downloaded:
                        download_url = f"https://www.yggtorrent.top/engine/download_torrent?id={file_id}"
                        driver.get(download_url)
                        logging.debug(f'Downloaded {file_id}')
                        time.sleep(2)  # Wait for download to start
                        downloaded.add(file_id)
                        with open(downloaded_file, 'w') as f:
                            json.dump(list(downloaded), f)
                        debug_print(f"Downloaded torrent for file_id {file_id}")
                        torrents_downloaded += 1

                        # Broadcast new torrent to web interface
                        if web_server:
                            category_id, name, file_id, timestamp, size, seeds = item
                            size_bytes = int(size)
                            seeds_int = int(seeds)
                            web_server.broadcast_torrent_added(name, file_id, size_bytes, category_id, seeds_int)
                    else:
                        debug_print(f"Torrent for file_id {file_id} already downloaded, skipping")
                        logging.debug(f'Skipped {file_id}')
                
                # Print ratio with asterisk if torrents were downloaded (when web interface is disabled)
                if torrents_downloaded > 0 and not args.debug and not web_server and 'ratio' in locals():
                    current_time = datetime.now().strftime('%d.%m.%Y-%H:%M:%S')
                    print(f"{ratio} - {current_time} *")

                time.sleep(10)  # Allow time for downloads to complete
                logging.debug('Waited for downloads')
                # print("Waiting 60 seconds before next refresh...")
                time.sleep(60)
                logging.debug('Waited 60 seconds')
            except Exception as inner_e:
                debug_print(f"Inner loop error: {inner_e}")
                logging.error(f'Inner error: {inner_e}')
                time.sleep(10)

except Exception as e:
    debug_print(f"An error occurred: {e}")
    logging.error(f'Error: {e}')

finally:
    if web_server:
        web_server.stop()
        debug_print("Web server stopped")
    driver.quit()
    logging.debug('Quit driver')
    debug_print("Browser closed")