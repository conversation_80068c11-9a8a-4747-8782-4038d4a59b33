<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YGG Torrent Bot Dashboard</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px 0;
            margin-bottom: 30px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.2s;
        }
        
        .stat-card:hover {
            transform: translateY(-2px);
        }
        
        .stat-value {
            font-size: 2.5em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .stat-label {
            font-size: 1.1em;
            color: #666;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .chart-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            height: 400px;
        }

        .chart-wrapper {
            position: relative;
            height: 300px;
            width: 100%;
        }
        
        .chart-title {
            font-size: 1.5em;
            margin-bottom: 20px;
            color: #333;
            text-align: center;
        }
        
        .table-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .table-header {
            background: #667eea;
            color: white;
            padding: 20px;
            font-size: 1.3em;
            text-align: center;
        }
        
        .table-wrapper {
            max-height: 600px;
            overflow-y: auto;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #333;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        tr:hover {
            background-color: #f8f9fa;
        }
        
        .torrent-name {
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 10px;
        }
        
        .status-online {
            background-color: #28a745;
        }
        
        .status-offline {
            background-color: #dc3545;
        }
        
        .connection-status {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 15px;
            border-radius: 5px;
            color: white;
            font-weight: bold;
            z-index: 1000;
        }
        
        .connected {
            background-color: #28a745;
        }
        
        .disconnected {
            background-color: #dc3545;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .chart-container, .table-container {
                padding: 15px;
                height: auto;
            }

            .chart-wrapper {
                height: 250px;
            }
            
            table {
                font-size: 0.9em;
            }
            
            .torrent-name {
                max-width: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="connection-status" id="connectionStatus">
        <span class="status-indicator" id="statusIndicator"></span>
        <span id="statusText">Connecting...</span>
    </div>
    
    <div class="container">
        <div class="header">
            <h1>YGG Torrent Bot Dashboard</h1>
            <p>Real-time monitoring of torrent downloads and ratio tracking</p>
        </div>
        
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value" id="totalTorrents">-</div>
                <div class="stat-label">Total Torrents</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="totalSize">-</div>
                <div class="stat-label">Total Size (GB)</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="currentRatio">-</div>
                <div class="stat-label">Current Ratio</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="ratioChange">-</div>
                <div class="stat-label">24h Change</div>
            </div>
        </div>
        
        <div class="chart-container">
            <div class="chart-title">Ratio History</div>
            <div class="chart-wrapper">
                <canvas id="ratioChart"></canvas>
            </div>
        </div>
        
        <div class="table-container">
            <div class="table-header">Recent Torrent Downloads</div>
            <div class="table-wrapper">
                <table>
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Size</th>
                            <th>Date Added</th>
                            <th>Category</th>
                            <th>Seeds</th>
                        </tr>
                    </thead>
                    <tbody id="torrentTableBody">
                        <tr>
                            <td colspan="5" style="text-align: center; padding: 40px;">Loading torrent data...</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        // Initialize Socket.IO connection
        const socket = io();
        
        // Chart.js setup
        const ctx = document.getElementById('ratioChart').getContext('2d');
        const ratioChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'Ratio',
                    data: [],
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: true,
                aspectRatio: 2,
                scales: {
                    x: {
                        display: true,
                        title: {
                            display: true,
                            text: 'Time'
                        },
                        ticks: {
                            maxTicksLimit: 10
                        }
                    },
                    y: {
                        display: true,
                        title: {
                            display: true,
                            text: 'Ratio'
                        },
                        beginAtZero: false,
                        suggestedMin: function(context) {
                            const data = context.chart.data.datasets[0].data;
                            if (data.length === 0) return 0;
                            const min = Math.min(...data);
                            return Math.max(0, min - 0.1);
                        },
                        suggestedMax: function(context) {
                            const data = context.chart.data.datasets[0].data;
                            if (data.length === 0) return 5;
                            const max = Math.max(...data);
                            return max + 0.1;
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                },
                elements: {
                    point: {
                        radius: 2,
                        hoverRadius: 4
                    }
                }
            }
        });
        
        // Connection status management
        function updateConnectionStatus(connected) {
            const statusElement = document.getElementById('connectionStatus');
            const indicatorElement = document.getElementById('statusIndicator');
            const textElement = document.getElementById('statusText');
            
            if (connected) {
                statusElement.className = 'connection-status connected';
                indicatorElement.className = 'status-indicator status-online';
                textElement.textContent = 'Connected';
            } else {
                statusElement.className = 'connection-status disconnected';
                indicatorElement.className = 'status-indicator status-offline';
                textElement.textContent = 'Disconnected';
            }
        }
        
        // Socket event handlers
        socket.on('connect', function() {
            console.log('Connected to server');
            updateConnectionStatus(true);
        });
        
        socket.on('disconnect', function() {
            console.log('Disconnected from server');
            updateConnectionStatus(false);
        });
        
        socket.on('stats_update', function(stats) {
            document.getElementById('totalTorrents').textContent = stats.total_torrents || '-';
            document.getElementById('totalSize').textContent = stats.total_size_gb || '-';
            document.getElementById('currentRatio').textContent = stats.latest_ratio || '-';
            
            const ratioChange = stats.ratio_change_24h;
            const changeElement = document.getElementById('ratioChange');
            if (ratioChange !== undefined && ratioChange !== null) {
                const sign = ratioChange >= 0 ? '+' : '';
                changeElement.textContent = sign + ratioChange.toFixed(3);
                changeElement.style.color = ratioChange >= 0 ? '#28a745' : '#dc3545';
            } else {
                changeElement.textContent = '-';
                changeElement.style.color = '#333';
            }
        });
        
        socket.on('ratio_update', function(ratioData) {
            // Limit to last 50 points for better performance and readability
            const limitedData = ratioData.slice(-50);

            const labels = limitedData.map(item => {
                const date = new Date(item.timestamp);
                return date.toLocaleDateString('fr-FR') + ' ' + date.toLocaleTimeString('fr-FR', {hour: '2-digit', minute:'2-digit'});
            });
            const data = limitedData.map(item => item.ratio);

            // Update chart data
            ratioChart.data.labels = labels;
            ratioChart.data.datasets[0].data = data;

            // Recalculate Y-axis range
            if (data.length > 0) {
                const min = Math.min(...data);
                const max = Math.max(...data);
                const padding = (max - min) * 0.1 || 0.1;

                ratioChart.options.scales.y.min = Math.max(0, min - padding);
                ratioChart.options.scales.y.max = max + padding;
            }

            ratioChart.update('none'); // Use 'none' animation for better performance
        });
        
        socket.on('torrent_update', function(torrents) {
            const tbody = document.getElementById('torrentTableBody');
            
            if (torrents.length === 0) {
                tbody.innerHTML = '<tr><td colspan="5" style="text-align: center; padding: 40px;">No torrents downloaded yet</td></tr>';
                return;
            }
            
            tbody.innerHTML = torrents.map(torrent => `
                <tr>
                    <td class="torrent-name" title="${torrent.name}">${torrent.name}</td>
                    <td>${torrent.size_display}</td>
                    <td>${torrent.date_added_display}</td>
                    <td>${torrent.category_id}</td>
                    <td>${torrent.seeds}</td>
                </tr>
            `).join('');
        });
        
        // Initial connection status
        updateConnectionStatus(false);
    </script>
</body>
</html>
