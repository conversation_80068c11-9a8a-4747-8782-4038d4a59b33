from flask import Flask, render_template, jsonify, request
from flask_socketio import <PERSON>cket<PERSON>, emit
import threading
import time
from data_manager import get_data_manager
import logging


class WebServer:
    """Flask web server for the torrent monitoring dashboard"""
    
    def __init__(self, host='127.0.0.1', port=8080, debug=False):
        self.host = host
        self.port = port
        self.debug = debug
        
        # Create Flask app
        self.app = Flask(__name__, template_folder='templates')
        self.app.config['SECRET_KEY'] = 'ygg-torrent-bot-secret-key'
        
        # Initialize SocketIO
        self.socketio = SocketIO(self.app, cors_allowed_origins="*", async_mode='threading')
        
        # Set up routes
        self._setup_routes()
        self._setup_socketio_events()
        
        # Server thread
        self.server_thread = None
        self.running = False
    
    def _setup_routes(self):
        """Set up Flask routes"""
        
        @self.app.route('/')
        def dashboard():
            """Main dashboard page"""
            return render_template('dashboard.html')
        
        @self.app.route('/api/stats')
        def api_stats():
            """Get summary statistics"""
            return jsonify(get_data_manager().get_stats())

        @self.app.route('/api/ratio-history')
        def api_ratio_history():
            """Get ratio history data"""
            limit = request.args.get('limit', type=int)
            return jsonify(get_data_manager().get_ratio_history(limit))

        @self.app.route('/api/torrent-log')
        def api_torrent_log():
            """Get torrent log data"""
            limit = request.args.get('limit', type=int, default=100)
            return jsonify(get_data_manager().get_torrent_log(limit))
        
        @self.app.route('/api/health')
        def api_health():
            """Health check endpoint"""
            return jsonify({'status': 'ok', 'timestamp': time.time()})
    
    def _setup_socketio_events(self):
        """Set up SocketIO event handlers"""
        
        @self.socketio.on('connect')
        def handle_connect():
            """Handle client connection"""
            if self.debug:
                print(f"Client connected: {request.sid}")
            
            # Send initial data to the connected client
            emit('stats_update', get_data_manager().get_stats())
            emit('ratio_update', get_data_manager().get_ratio_history(50))
            emit('torrent_update', get_data_manager().get_torrent_log(20))
        
        @self.socketio.on('disconnect')
        def handle_disconnect():
            """Handle client disconnection"""
            if self.debug:
                print(f"Client disconnected: {request.sid}")
        
        @self.socketio.on('request_data')
        def handle_request_data(data):
            """Handle data request from client"""
            data_type = data.get('type')
            limit = data.get('limit')
            
            if data_type == 'ratio':
                emit('ratio_update', get_data_manager().get_ratio_history(limit))
            elif data_type == 'torrents':
                emit('torrent_update', get_data_manager().get_torrent_log(limit))
            elif data_type == 'stats':
                emit('stats_update', get_data_manager().get_stats())
    
    def broadcast_ratio_update(self, ratio, timestamp_str):
        """Broadcast ratio update to all connected clients"""
        if self.running:
            # Update data manager
            get_data_manager().update_ratio(ratio, timestamp_str)

            # Broadcast to all clients
            self.socketio.emit('ratio_update', get_data_manager().get_ratio_history(50))
            self.socketio.emit('stats_update', get_data_manager().get_stats())

    def broadcast_torrent_added(self, name, file_id, size_bytes, category_id, seeds):
        """Broadcast new torrent addition to all connected clients"""
        if self.running:
            # Update data manager
            get_data_manager().add_torrent_log_entry(name, file_id, size_bytes, category_id, seeds)

            # Broadcast to all clients
            self.socketio.emit('torrent_update', get_data_manager().get_torrent_log(20))
            self.socketio.emit('stats_update', get_data_manager().get_stats())
    
    def start(self):
        """Start the web server in a separate thread"""
        if self.running:
            return
        
        self.running = True
        
        def run_server():
            try:
                if self.debug:
                    print(f"Starting web server on http://{self.host}:{self.port}")
                
                # Disable Flask's default logging in non-debug mode
                if not self.debug:
                    log = logging.getLogger('werkzeug')
                    log.setLevel(logging.ERROR)
                
                self.socketio.run(
                    self.app,
                    host=self.host,
                    port=self.port,
                    debug=False,  # Always False to prevent conflicts
                    use_reloader=False,
                    log_output=self.debug
                )
            except Exception as e:
                if self.debug:
                    print(f"Web server error: {e}")
                self.running = False
        
        self.server_thread = threading.Thread(target=run_server, daemon=True)
        self.server_thread.start()
        
        # Give the server a moment to start
        time.sleep(1)
        
        if self.debug:
            print(f"Web server started successfully on http://{self.host}:{self.port}")
    
    def stop(self):
        """Stop the web server"""
        if self.running:
            self.running = False
            if self.debug:
                print("Web server stopped")
    
    def is_running(self):
        """Check if the web server is running"""
        return self.running and self.server_thread and self.server_thread.is_alive()


# Global web server instance
web_server = None


def initialize_web_server(host='127.0.0.1', port=8080, debug=False):
    """Initialize the global web server instance"""
    global web_server
    web_server = WebServer(host, port, debug)
    return web_server


def get_web_server():
    """Get the global web server instance"""
    return web_server
