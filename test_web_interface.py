#!/usr/bin/env python3
"""
Test script for the web interface
"""

import time
import sys
from data_manager import get_data_manager
from web_server import initialize_web_server

def test_web_interface():
    """Test the web interface functionality"""
    print("Testing YGG Torrent Bot Web Interface")
    print("=" * 50)
    
    try:
        # Test data manager
        print("1. Testing data manager...")
        dm = get_data_manager()
        stats = dm.get_stats()
        print(f"   ✓ Initial stats: {stats}")

        # Add some test data
        print("2. Adding test data...")
        dm.add_torrent_log_entry(
            "Test Torrent 1",
            "test123",
            1073741824,  # 1GB
            "2161",
            5
        )
        dm.update_ratio(2.5, "08.08.2025-20:00:00")

        updated_stats = dm.get_stats()
        print(f"   ✓ Updated stats: {updated_stats}")
        
        # Test web server
        print("3. Testing web server...")
        web_server = initialize_web_server('127.0.0.1', 8081, True)
        web_server.start()
        
        if web_server.is_running():
            print("   ✓ Web server started successfully")
            print(f"   ✓ Access the dashboard at: http://127.0.0.1:8081")
            
            # Keep server running for a bit
            print("4. Server running for 30 seconds...")
            print("   You can open http://127.0.0.1:8081 in your browser to test")
            
            for i in range(30, 0, -1):
                print(f"   Stopping in {i} seconds...", end='\r')
                time.sleep(1)
            
            print("\n5. Stopping web server...")
            web_server.stop()
            print("   ✓ Web server stopped")
            
        else:
            print("   ✗ Web server failed to start")
            return False
            
    except Exception as e:
        print(f"   ✗ Error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print("\n✓ All tests passed!")
    return True

if __name__ == "__main__":
    success = test_web_interface()
    sys.exit(0 if success else 1)
