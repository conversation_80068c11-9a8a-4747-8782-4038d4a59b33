#!/usr/bin/env python3
"""
Test script for the fixed web interface
"""

import time
import sys
from data_manager import get_data_manager
from web_server import initialize_web_server

def test_fixed_interface():
    """Test the fixed web interface"""
    print("Testing Fixed YGG Torrent Bot Web Interface")
    print("=" * 50)
    
    try:
        # Test data manager
        print("1. Testing data manager...")
        dm = get_data_manager()
        
        # Clean any existing test data
        dm.clean_test_data()
        
        stats = dm.get_stats()
        print(f"   ✓ Clean stats: {stats}")
        
        # Test web server
        print("2. Testing web server...")
        web_server = initialize_web_server('127.0.0.1', 8082, True)
        web_server.start()
        
        if web_server.is_running():
            print("   ✓ Web server started successfully")
            print(f"   ✓ Access the dashboard at: http://127.0.0.1:8082")
            print("   ✓ Chart should now have proper height constraints")
            print("   ✓ Torrent table should show only real data")
            
            # Keep server running for testing
            print("3. Server running for 60 seconds for testing...")
            print("   Open http://127.0.0.1:8082 in your browser to verify fixes")
            
            for i in range(60, 0, -1):
                print(f"   Stopping in {i} seconds...", end='\r')
                time.sleep(1)
            
            print("\n4. Stopping web server...")
            web_server.stop()
            print("   ✓ Web server stopped")
            
        else:
            print("   ✗ Web server failed to start")
            return False
            
    except Exception as e:
        print(f"   ✗ Error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print("\n✓ All tests passed! Issues should be fixed.")
    return True

if __name__ == "__main__":
    success = test_fixed_interface()
    sys.exit(0 if success else 1)
