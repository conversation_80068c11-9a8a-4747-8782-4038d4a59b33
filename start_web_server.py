#!/usr/bin/env python3
"""
Start the web server for testing
"""

import time
from data_manager import get_data_manager
from web_server import initialize_web_server

def main():
    print("Starting YGG Torrent Bot Web Interface...")
    
    # Add some test data
    dm = get_data_manager()
    dm.add_torrent_log_entry("Test Movie 1080p", "test001", 2147483648, "2161", 3)
    dm.add_torrent_log_entry("Test Series S01E01", "test002", 1073741824, "2172", 1)
    dm.update_ratio(2.68, "08.08.2025-20:30:00")
    
    # Start web server
    web_server = initialize_web_server('127.0.0.1', 8080, True)
    web_server.start()
    
    print("Web server started at http://127.0.0.1:8080")
    print("Press Ctrl+C to stop")
    
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\nStopping web server...")
        web_server.stop()
        print("Web server stopped")

if __name__ == "__main__":
    main()
