#!/usr/bin/env python3
"""
Verification script to ensure all fixes are working
"""

import json
import os
from data_manager import get_data_manager

def verify_fixes():
    """Verify that all fixes are working correctly"""
    print("Verifying YGG Torrent Bot Fixes")
    print("=" * 40)
    
    # 1. Check torrent log for test data
    print("1. Checking torrent log for test data...")
    dm = get_data_manager()
    torrent_log = dm.get_torrent_log()
    
    test_entries = [entry for entry in torrent_log if entry.get('file_id', '').startswith('test')]
    if test_entries:
        print(f"   ⚠ Found {len(test_entries)} test entries - cleaning...")
        dm.clean_test_data()
        print("   ✓ Test data cleaned")
    else:
        print("   ✓ No test data found")
    
    # 2. Check ratio data
    print("2. Checking ratio data...")
    ratio_history = dm.get_ratio_history()
    print(f"   ✓ Found {len(ratio_history)} ratio entries")
    
    if ratio_history:
        latest_ratio = ratio_history[-1]['ratio']
        print(f"   ✓ Latest ratio: {latest_ratio}")
    
    # 3. Check stats
    print("3. Checking statistics...")
    stats = dm.get_stats()
    print(f"   ✓ Total torrents: {stats['total_torrents']}")
    print(f"   ✓ Total size: {stats['total_size_gb']} GB")
    print(f"   ✓ Latest ratio: {stats['latest_ratio']}")
    print(f"   ✓ 24h change: {stats['ratio_change_24h']}")
    
    # 4. Verify file structure
    print("4. Verifying file structure...")
    required_files = [
        'bot.py',
        'data_manager.py', 
        'web_server.py',
        'templates/dashboard.html',
        'config.yaml'
    ]
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"   ✓ {file_path}")
        else:
            print(f"   ✗ {file_path} missing")
    
    print("\n" + "=" * 40)
    print("FIXES SUMMARY:")
    print("✓ Fixed: Fausses données dans 'Recent Torrent Downloads'")
    print("  - Filtre automatique des données de test")
    print("  - Nettoyage au démarrage du bot")
    print()
    print("✓ Fixed: Graphique qui s'étire en hauteur")
    print("  - Contraintes de hauteur CSS ajoutées")
    print("  - Ratio d'aspect fixe (2:1)")
    print("  - Limites d'axe Y dynamiques")
    print("  - Limitation à 50 points max pour performance")
    print()
    print("🚀 L'interface web est maintenant corrigée !")
    print("   Lancez: python3 bot.py")
    print("   Accédez à: http://127.0.0.1:8080")

if __name__ == "__main__":
    verify_fixes()
